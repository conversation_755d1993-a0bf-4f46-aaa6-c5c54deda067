.products-page {
  padding: 40px 0;
  min-height: calc(100vh - 160px);
}

.products-header {
  text-align: center;
  margin-bottom: 40px;
}

.products-header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
}

.products-header p {
  color: #666;
  font-size: 1.1rem;
}

.products-content {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 40px;
}

/* Filters Sidebar */
.filters-sidebar {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  height: fit-content;
  position: sticky;
  top: 100px;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.filters-header h3 {
  color: #333;
  margin: 0;
}

.close-filters {
  display: none;
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #666;
  cursor: pointer;
}

.filter-group {
  margin-bottom: 25px;
}

.filter-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.filter-input,
.filter-select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: border-color 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #007bff;
}

.price-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.clear-filters-btn {
  width: 100%;
  padding: 12px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.clear-filters-btn:hover {
  background: #c82333;
}

/* Products Main */
.products-main {
  min-height: 600px;
}

.products-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.filters-toggle {
  display: none;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  gap: 8px;
  align-items: center;
}

.sort-select {
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  font-size: 0.95rem;
  min-width: 200px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.loading-container {
  text-align: center;
  padding: 80px 20px;
}

.loading-container p {
  margin-top: 20px;
  color: #666;
  font-size: 1.1rem;
}

.no-products {
  text-align: center;
  padding: 80px 20px;
  color: #666;
}

.no-products svg {
  color: #ccc;
  margin-bottom: 20px;
}

.no-products h3 {
  margin-bottom: 10px;
  color: #333;
}

.no-products p {
  margin-bottom: 30px;
  font-size: 1.1rem;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 40px;
}

.pagination-btn {
  padding: 10px 15px;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.pagination-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .products-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .filters-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background: white;
    padding: 20px;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
  }

  .filters-sidebar.show {
    transform: translateX(0);
  }

  .close-filters {
    display: block;
  }

  .filters-toggle {
    display: flex;
  }

  .products-controls {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .sort-select {
    min-width: auto;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }

  .pagination {
    flex-wrap: wrap;
    gap: 5px;
  }

  .pagination-btn {
    padding: 8px 12px;
    font-size: 0.9rem;
  }
}
