const express = require('express');
const { body } = require('express-validator');
const {
  newOrder,
  getSingleOrder,
  myOrders,
  allOrders,
  updateOrder,
  deleteOrder
} = require('../controllers/orderController');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const orderValidation = [
  body('orderItems')
    .isArray({ min: 1 })
    .withMessage('Order must contain at least one item'),
  body('shippingInfo.address')
    .trim()
    .notEmpty()
    .withMessage('Shipping address is required'),
  body('shippingInfo.city')
    .trim()
    .notEmpty()
    .withMessage('City is required'),
  body('shippingInfo.state')
    .trim()
    .notEmpty()
    .withMessage('State is required'),
  body('shippingInfo.country')
    .trim()
    .notEmpty()
    .withMessage('Country is required'),
  body('shippingInfo.pinCode')
    .trim()
    .notEmpty()
    .withMessage('Pin code is required'),
  body('shippingInfo.phoneNo')
    .trim()
    .notEmpty()
    .withMessage('Phone number is required'),
  body('itemsPrice')
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage('Items price must be a positive number'),
  body('taxPrice')
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage('Tax price must be a positive number'),
  body('shippingPrice')
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage('Shipping price must be a positive number'),
  body('totalPrice')
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage('Total price must be a positive number'),
  body('paymentInfo.id')
    .notEmpty()
    .withMessage('Payment ID is required'),
  body('paymentInfo.status')
    .notEmpty()
    .withMessage('Payment status is required')
];

// Protected routes
router.route('/').post(protect, orderValidation, newOrder);
router.route('/me').get(protect, myOrders);
router.route('/:id').get(protect, getSingleOrder);

// Admin routes
router.route('/admin/orders').get(protect, authorize('admin'), allOrders);
router.route('/admin/orders/:id').put(protect, authorize('admin'), updateOrder);
router.route('/admin/orders/:id').delete(protect, authorize('admin'), deleteOrder);

module.exports = router;
