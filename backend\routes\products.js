const express = require('express');
const { body } = require('express-validator');
const {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  createProductReview,
  getProductReviews,
  deleteReview
} = require('../controllers/productController');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const productValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Product name must be between 2 and 100 characters'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),
  body('price')
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('category')
    .notEmpty()
    .withMessage('Category is required'),
  body('seller')
    .trim()
    .notEmpty()
    .withMessage('Seller is required'),
  body('stock')
    .isInt({ min: 0 })
    .withMessage('Stock must be a non-negative integer')
];

const reviewValidation = [
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('comment')
    .trim()
    .isLength({ min: 5, max: 500 })
    .withMessage('Comment must be between 5 and 500 characters')
];

// Public routes
router.route('/').get(getProducts);
router.route('/:id').get(getProduct);
router.route('/:id/reviews').get(getProductReviews);

// Protected routes
router.route('/:id/reviews').put(protect, reviewValidation, createProductReview);
router.route('/:id/reviews').delete(protect, deleteReview);

// Admin routes
router.route('/').post(protect, authorize('admin'), productValidation, createProduct);
router.route('/:id').put(protect, authorize('admin'), updateProduct);
router.route('/:id').delete(protect, authorize('admin'), deleteProduct);

module.exports = router;
