.home {
  padding: 0;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 100px 0;
  min-height: 600px;
  display: flex;
  align-items: center;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-text h1 {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-text p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  line-height: 1.6;
  opacity: 0.9;
}

.hero-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  font-size: 1.1rem;
  padding: 15px 30px;
  background: white;
  color: #667eea;
  border-radius: 50px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.hero-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  color: #667eea;
}

.hero-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

/* Features Section */
.features {
  padding: 80px 0;
  background: white;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 60px;
  color: #333;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.feature-card {
  text-align: center;
  padding: 40px 20px;
  border-radius: 15px;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  background: white;
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  font-size: 2rem;
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* Featured Products Section */
.featured-products {
  padding: 80px 0;
  background: #f8f9fa;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.no-products {
  text-align: center;
  color: #666;
  font-size: 1.1rem;
  grid-column: 1 / -1;
  padding: 40px;
}

/* About Section */
.about {
  padding: 80px 0;
  background: white;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.about-text h2 {
  font-size: 2.5rem;
  margin-bottom: 30px;
  color: #333;
}

.about-text p {
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 20px;
  color: #666;
}

.about-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 15px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.loading-container {
  text-align: center;
  padding: 60px 20px;
  grid-column: 1 / -1;
}

.loading-container p {
  margin-top: 20px;
  color: #666;
  font-size: 1.1rem;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .hero {
    padding: 60px 0;
    min-height: auto;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-text h1 {
    font-size: 2.5rem;
  }

  .hero-text p {
    font-size: 1.1rem;
  }

  .hero-image img {
    height: 300px;
  }

  .features {
    padding: 60px 0;
  }

  .section-title {
    font-size: 2rem;
    margin-bottom: 40px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .featured-products {
    padding: 60px 0;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }

  .about {
    padding: 60px 0;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .about-text h2 {
    font-size: 2rem;
  }

  .about-image img {
    height: 300px;
  }
}
