import React, { createContext, useContext, useReducer, useEffect } from 'react';

const CartContext = createContext();

const initialState = {
  cartItems: JSON.parse(localStorage.getItem('cartItems')) || [],
  shippingInfo: JSON.parse(localStorage.getItem('shippingInfo')) || {},
  itemsPrice: 0,
  shippingPrice: 0,
  taxPrice: 0,
  totalPrice: 0
};

const cartReducer = (state, action) => {
  switch (action.type) {
    case 'ADD_TO_CART':
      const item = action.payload;
      const existItem = state.cartItems.find(x => x.product === item.product);

      if (existItem) {
        return {
          ...state,
          cartItems: state.cartItems.map(x =>
            x.product === existItem.product ? item : x
          )
        };
      } else {
        return {
          ...state,
          cartItems: [...state.cartItems, item]
        };
      }

    case 'REMOVE_FROM_CART':
      return {
        ...state,
        cartItems: state.cartItems.filter(x => x.product !== action.payload)
      };

    case 'UPDATE_CART_QUANTITY':
      return {
        ...state,
        cartItems: state.cartItems.map(item =>
          item.product === action.payload.id
            ? { ...item, quantity: action.payload.quantity }
            : item
        )
      };

    case 'CLEAR_CART':
      return {
        ...state,
        cartItems: []
      };

    case 'SAVE_SHIPPING_INFO':
      return {
        ...state,
        shippingInfo: action.payload
      };

    case 'CALCULATE_PRICES':
      const itemsPrice = state.cartItems.reduce(
        (acc, item) => acc + item.price * item.quantity,
        0
      );
      
      const shippingPrice = itemsPrice > 100 ? 0 : 10;
      const taxPrice = Number((0.15 * itemsPrice).toFixed(2));
      const totalPrice = Number((itemsPrice + shippingPrice + taxPrice).toFixed(2));

      return {
        ...state,
        itemsPrice: Number(itemsPrice.toFixed(2)),
        shippingPrice,
        taxPrice,
        totalPrice
      };

    default:
      return state;
  }
};

export const CartProvider = ({ children }) => {
  const [state, dispatch] = useReducer(cartReducer, initialState);

  // Calculate prices whenever cart items change
  useEffect(() => {
    dispatch({ type: 'CALCULATE_PRICES' });
  }, [state.cartItems]);

  // Save cart items to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('cartItems', JSON.stringify(state.cartItems));
  }, [state.cartItems]);

  // Save shipping info to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('shippingInfo', JSON.stringify(state.shippingInfo));
  }, [state.shippingInfo]);

  const addToCart = (product, quantity) => {
    const item = {
      product: product._id,
      name: product.name,
      price: product.price,
      image: product.images[0]?.url,
      stock: product.stock,
      quantity
    };

    dispatch({
      type: 'ADD_TO_CART',
      payload: item
    });
  };

  const removeFromCart = (id) => {
    dispatch({
      type: 'REMOVE_FROM_CART',
      payload: id
    });
  };

  const updateCartQuantity = (id, quantity) => {
    dispatch({
      type: 'UPDATE_CART_QUANTITY',
      payload: { id, quantity }
    });
  };

  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };

  const saveShippingInfo = (data) => {
    dispatch({
      type: 'SAVE_SHIPPING_INFO',
      payload: data
    });
  };

  const getCartItemsCount = () => {
    return state.cartItems.reduce((acc, item) => acc + item.quantity, 0);
  };

  const isInCart = (productId) => {
    return state.cartItems.some(item => item.product === productId);
  };

  const getCartItem = (productId) => {
    return state.cartItems.find(item => item.product === productId);
  };

  const value = {
    ...state,
    addToCart,
    removeFromCart,
    updateCartQuantity,
    clearCart,
    saveShippingInfo,
    getCartItemsCount,
    isInCart,
    getCartItem
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
