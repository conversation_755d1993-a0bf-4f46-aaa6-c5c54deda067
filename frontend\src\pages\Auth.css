.auth-container {
  min-height: calc(100vh - 160px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 450px;
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h1 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 10px;
  font-weight: 700;
}

.auth-header p {
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}

.auth-form {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 25px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

.form-label svg {
  color: #007bff;
  font-size: 0.9rem;
}

.form-control {
  width: 100%;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  background: white;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 1rem;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: #007bff;
}

.auth-btn {
  width: 100%;
  padding: 15px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 10px;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.auth-btn:hover:not(.loading) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.auth-btn.loading {
  opacity: 0.8;
  cursor: not-allowed;
}

.auth-btn .spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.auth-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.auth-footer p {
  color: #666;
  margin: 0;
}

.auth-link {
  color: #007bff;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

.demo-credentials {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.demo-credentials h4 {
  color: #333;
  margin-bottom: 15px;
  text-align: center;
  font-size: 1rem;
}

.demo-section {
  margin-bottom: 15px;
}

.demo-section:last-child {
  margin-bottom: 0;
}

.demo-section strong {
  color: #007bff;
  display: block;
  margin-bottom: 5px;
}

.demo-section p {
  margin: 2px 0;
  font-size: 0.9rem;
  color: #666;
  font-family: 'Courier New', monospace;
  background: white;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .auth-container {
    padding: 20px 15px;
    min-height: calc(100vh - 140px);
  }

  .auth-card {
    padding: 30px 25px;
    border-radius: 15px;
  }

  .auth-header h1 {
    font-size: 1.75rem;
  }

  .form-control {
    padding: 12px;
    font-size: 0.95rem;
  }

  .auth-btn {
    padding: 12px;
    font-size: 1rem;
  }

  .demo-credentials {
    padding: 15px;
  }

  .demo-section p {
    font-size: 0.8rem;
  }
}
