.product-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.product-link {
  text-decoration: none;
  color: inherit;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-image {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.out-of-stock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.1rem;
}

.discount-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: #dc3545;
  color: white;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.product-info {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-category {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 12px;
  text-transform: capitalize;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 0.9rem;
}

.star.filled {
  color: #ffc107;
}

.star.half {
  color: #ffc107;
}

.star.empty {
  color: #e9ecef;
}

.rating-text {
  font-size: 0.8rem;
  color: #666;
}

.product-price {
  margin-bottom: 12px;
}

.current-price {
  font-size: 1.3rem;
  font-weight: bold;
  color: #007bff;
}

.original-price {
  font-size: 1rem;
  color: #999;
  text-decoration: line-through;
  margin-left: 8px;
}

.product-stock {
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.in-stock {
  color: #28a745;
  font-weight: 500;
}

.out-of-stock {
  color: #dc3545;
  font-weight: 500;
}

.product-actions {
  padding: 0 20px 20px;
}

.add-to-cart-btn {
  width: 100%;
  padding: 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.add-to-cart-btn:hover:not(.disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.add-to-cart-btn.disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .product-image {
    height: 200px;
  }

  .product-info {
    padding: 15px;
  }

  .product-name {
    font-size: 1rem;
  }

  .current-price {
    font-size: 1.2rem;
  }

  .product-actions {
    padding: 0 15px 15px;
  }

  .add-to-cart-btn {
    padding: 10px;
    font-size: 0.9rem;
  }
}
