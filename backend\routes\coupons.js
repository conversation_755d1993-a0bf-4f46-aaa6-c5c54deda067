const express = require('express');
const { body } = require('express-validator');
const {
  createCoupon,
  getAll<PERSON>oupons,
  getCoupon,
  updateCoupon,
  deleteCoupon,
  validateCoupon
} = require('../controllers/couponController');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const couponValidation = [
  body('code')
    .trim()
    .isLength({ min: 3, max: 20 })
    .isAlphanumeric()
    .withMessage('Coupon code must be 3-20 alphanumeric characters'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 200 })
    .withMessage('Description must be between 10 and 200 characters'),
  body('discountType')
    .isIn(['percentage', 'fixed'])
    .withMessage('Discount type must be either percentage or fixed'),
  body('discountValue')
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage('Discount value must be a positive number'),
  body('validFrom')
    .isISO8601()
    .withMessage('Valid from date must be a valid date'),
  body('validUntil')
    .isISO8601()
    .withMessage('Valid until date must be a valid date')
    .custom((value, { req }) => {
      if (new Date(value) <= new Date(req.body.validFrom)) {
        throw new Error('Valid until date must be after valid from date');
      }
      return true;
    })
];

// Public routes
router.route('/validate').post(protect, validateCoupon);

// Admin routes
router.route('/admin/coupons').get(protect, authorize('admin'), getAllCoupons);
router.route('/admin/coupons').post(protect, authorize('admin'), couponValidation, createCoupon);
router.route('/admin/coupons/:id').get(protect, authorize('admin'), getCoupon);
router.route('/admin/coupons/:id').put(protect, authorize('admin'), updateCoupon);
router.route('/admin/coupons/:id').delete(protect, authorize('admin'), deleteCoupon);

module.exports = router;
