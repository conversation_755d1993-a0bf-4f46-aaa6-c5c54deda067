import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { FaShoppingBag, FaShippingFast, FaHeadset, FaShieldAlt } from 'react-icons/fa';
import { productService } from '../services/productService';
import ProductCard from '../components/product/ProductCard';
import './Home.css';

const Home = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      try {
        const response = await productService.getProducts({ 
          featured: true, 
          limit: 8 
        });
        setFeaturedProducts(response.data.data);
      } catch (error) {
        console.error('Error fetching featured products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProducts();
  }, []);

  return (
    <>
      <Helmet>
        <title>ShopEasy - Your One-Stop Shopping Destination</title>
        <meta name="description" content="Discover amazing products at great prices. Shop electronics, fashion, home goods and more with fast shipping and excellent customer service." />
      </Helmet>

      <div className="home">
        {/* Hero Section */}
        <section className="hero">
          <div className="container">
            <div className="hero-content">
              <div className="hero-text">
                <h1>Welcome to ShopEasy</h1>
                <p>
                  Discover amazing products at unbeatable prices. 
                  From electronics to fashion, we have everything you need.
                </p>
                <Link to="/products" className="btn btn-primary hero-btn">
                  <FaShoppingBag /> Explore Products
                </Link>
              </div>
              <div className="hero-image">
                <img 
                  src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                  alt="Shopping" 
                />
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="features">
          <div className="container">
            <h2 className="section-title">Why Choose ShopEasy?</h2>
            <div className="features-grid">
              <div className="feature-card">
                <div className="feature-icon">
                  <FaShippingFast />
                </div>
                <h3>Fast Shipping</h3>
                <p>Free shipping on orders over $100. Get your products delivered quickly and safely.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">
                  <FaHeadset />
                </div>
                <h3>24/7 Support</h3>
                <p>Our customer support team is available round the clock to help you with any queries.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">
                  <FaShieldAlt />
                </div>
                <h3>Secure Payment</h3>
                <p>Your payment information is secure with our encrypted payment processing system.</p>
              </div>
              <div className="feature-card">
                <div className="feature-icon">
                  <FaShoppingBag />
                </div>
                <h3>Quality Products</h3>
                <p>We carefully curate our products to ensure you get the best quality at great prices.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Products Section */}
        <section className="featured-products">
          <div className="container">
            <h2 className="section-title">Featured Products</h2>
            {loading ? (
              <div className="loading-container">
                <div className="spinner"></div>
                <p>Loading featured products...</p>
              </div>
            ) : (
              <div className="products-grid">
                {featuredProducts.length > 0 ? (
                  featuredProducts.map(product => (
                    <ProductCard key={product._id} product={product} />
                  ))
                ) : (
                  <p className="no-products">No featured products available at the moment.</p>
                )}
              </div>
            )}
            <div className="text-center mt-4">
              <Link to="/products" className="btn btn-outline">
                View All Products
              </Link>
            </div>
          </div>
        </section>

        {/* About Section */}
        <section className="about">
          <div className="container">
            <div className="about-content">
              <div className="about-text">
                <h2>About ShopEasy</h2>
                <p>
                  ShopEasy is your trusted online shopping destination, offering a wide range of 
                  high-quality products at competitive prices. We are committed to providing 
                  exceptional customer service and a seamless shopping experience.
                </p>
                <p>
                  With thousands of products across multiple categories, fast shipping, and 
                  secure payment options, we make online shopping easy and enjoyable for everyone.
                </p>
                <Link to="/contact" className="btn btn-secondary">
                  Contact Us
                </Link>
              </div>
              <div className="about-image">
                <img 
                  src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                  alt="About Us" 
                />
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Home;
