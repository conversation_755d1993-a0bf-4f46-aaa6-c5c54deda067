{"name": "ecommerce-backend", "version": "1.0.0", "description": "Backend API for MERN e-commerce application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "nodemailer": "^6.9.7", "stripe": "^14.7.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["express", "mongodb", "api", "ecommerce"], "author": "Your Name", "license": "MIT"}