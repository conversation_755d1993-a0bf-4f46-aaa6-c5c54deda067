import api from './api';

export const couponService = {
  // Validate coupon
  validateCoupon: async (code, orderAmount) => {
    const response = await api.post('/coupons/validate', {
      code,
      orderAmount
    });
    return response;
  },

  // Get all coupons (Admin)
  getAllCoupons: async () => {
    const response = await api.get('/coupons/admin/coupons');
    return response;
  },

  // Get single coupon (Admin)
  getCoupon: async (id) => {
    const response = await api.get(`/coupons/admin/coupons/${id}`);
    return response;
  },

  // Create coupon (Admin)
  createCoupon: async (couponData) => {
    const response = await api.post('/coupons/admin/coupons', couponData);
    return response;
  },

  // Update coupon (Admin)
  updateCoupon: async (id, couponData) => {
    const response = await api.put(`/coupons/admin/coupons/${id}`, couponData);
    return response;
  },

  // Delete coupon (Admin)
  deleteCoupon: async (id) => {
    const response = await api.delete(`/coupons/admin/coupons/${id}`);
    return response;
  }
};
