import api from './api';

export const orderService = {
  // Create new order
  createOrder: async (orderData) => {
    const response = await api.post('/orders', orderData);
    return response;
  },

  // Get user orders
  getMyOrders: async () => {
    const response = await api.get('/orders/me');
    return response;
  },

  // Get single order
  getOrder: async (id) => {
    const response = await api.get(`/orders/${id}`);
    return response;
  },

  // Get all orders (Admin)
  getAllOrders: async () => {
    const response = await api.get('/orders/admin/orders');
    return response;
  },

  // Update order (Admin)
  updateOrder: async (id, status) => {
    const response = await api.put(`/orders/admin/orders/${id}`, { status });
    return response;
  },

  // Delete order (Admin)
  deleteOrder: async (id) => {
    const response = await api.delete(`/orders/admin/orders/${id}`);
    return response;
  }
};
