import api from './api';

export const productService = {
  // Get all products
  getProducts: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const response = await api.get(`/products?${queryString}`);
    return response;
  },

  // Get single product
  getProduct: async (id) => {
    const response = await api.get(`/products/${id}`);
    return response;
  },

  // Create product (Admin)
  createProduct: async (productData) => {
    const response = await api.post('/products', productData);
    return response;
  },

  // Update product (Admin)
  updateProduct: async (id, productData) => {
    const response = await api.put(`/products/${id}`, productData);
    return response;
  },

  // Delete product (Admin)
  deleteProduct: async (id) => {
    const response = await api.delete(`/products/${id}`);
    return response;
  },

  // Create product review
  createReview: async (id, reviewData) => {
    const response = await api.put(`/products/${id}/reviews`, reviewData);
    return response;
  },

  // Get product reviews
  getReviews: async (id) => {
    const response = await api.get(`/products/${id}/reviews`);
    return response;
  },

  // Delete review
  deleteReview: async (productId, reviewId) => {
    const response = await api.delete(`/products/${productId}/reviews?id=${reviewId}`);
    return response;
  }
};
