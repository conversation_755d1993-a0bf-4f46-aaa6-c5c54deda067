{"name": "mern-ecommerce", "version": "1.0.0", "description": "Complete MERN stack e-commerce application", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend\" \"npm run frontend\" \"npm run admin\"", "backend": "cd backend && npm run dev", "frontend": "cd frontend && npm run dev", "admin": "cd admin && npm run dev", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install && cd ../admin && npm install", "build": "cd frontend && npm run build && cd ../admin && npm run build"}, "keywords": ["mern", "ecommerce", "react", "nodejs", "mongodb"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}