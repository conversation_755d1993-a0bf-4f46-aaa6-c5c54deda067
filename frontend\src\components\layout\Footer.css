.footer {
  background: #2c3e50;
  color: white;
  padding: 40px 0 0;
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 30px;
}

.footer-section h3 {
  color: #007bff;
  margin-bottom: 20px;
  font-size: 24px;
}

.footer-section h4 {
  margin-bottom: 20px;
  font-size: 18px;
  color: #ecf0f1;
}

.footer-section p {
  line-height: 1.6;
  margin-bottom: 20px;
  color: #bdc3c7;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #34495e;
  color: white;
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: #007bff;
  transform: translateY(-2px);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #007bff;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #bdc3c7;
}

.contact-item svg {
  color: #007bff;
  font-size: 16px;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding: 20px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-bottom p {
  color: #bdc3c7;
  margin: 0;
}

.payment-methods {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #bdc3c7;
}

.payment-icons {
  display: flex;
  gap: 5px;
}

.payment-icon {
  font-size: 20px;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .footer {
    padding: 30px 0 0;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }
}
